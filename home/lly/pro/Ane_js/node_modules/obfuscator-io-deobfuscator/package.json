{"name": "obfuscator-io-deobfuscator", "version": "1.0.6", "description": "A deobfuscator for scripts obfuscated by Obfuscator.io", "main": "dist/index.js", "bin": "dist/cli.js", "types": "dist/index.d.ts", "scripts": {"test": "tsc && node dist/test.js", "prepare": "tsc"}, "author": "<PERSON>", "license": "ISC", "dependencies": {"@babel/generator": "^7.22.3", "@babel/parser": "^7.22.4", "@babel/traverse": "^7.22.4", "@babel/types": "^7.22.4", "@codemod/matchers": "^1.7.1", "@types/node": "^20.2.5", "commander": "^12.0.0"}, "devDependencies": {"@types/babel__core": "^7.20.1", "assert": "^2.1.0", "path-browserify": "^1.0.1", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.4.4", "typescript": "^5.9.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}}