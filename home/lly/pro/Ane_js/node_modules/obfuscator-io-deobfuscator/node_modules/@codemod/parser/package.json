{"name": "@codemod/parser", "version": "1.4.1", "description": "Wrapper around @babel/parser that allows parsing everything.", "repository": "https://github.com/codemod-js/codemod", "license": "Apache-2.0", "author": "<PERSON>", "main": "build/index.js", "types": "build/index.d.ts", "files": ["build"], "scripts": {"build": "tsc --build tsconfig.build.json", "clean": "rm -rf build tsconfig.build.tsbuildinfo", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "pnpm clean && pnpm build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "dependencies": {"@babel/parser": "^7.20.15"}, "devDependencies": {"@babel/types": "^7.20.7", "@types/jest": "^25.1.0", "@types/node": "^18.14.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "typescript": "^4.9.5"}, "publishConfig": {"access": "public"}, "gitHead": "7d754b551d0c70c508892dccc10382bab91356f2"}