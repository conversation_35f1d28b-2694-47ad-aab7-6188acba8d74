"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Matcher = void 0;
class Matcher {
    match(value, keys = []) {
        return this.matchValue(value, keys);
    }
    matchValue(
    /* eslint-disable @typescript-eslint/no-unused-vars */
    value, keys
    /* eslint-enable @typescript-eslint/no-unused-vars */
    ) {
        throw new Error(`${this.constructor.name}#matchValue is not implemented`);
    }
}
exports.Matcher = Matcher;
//# sourceMappingURL=Matcher.js.map