"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.matchPath = void 0;
function matchPath(matcher, captures, value, callback) {
    const toMatch = Array.isArray(value)
        ? value.map((element) => element.node)
        : value.node;
    if (matcher.match(toMatch)) {
        const capturedPaths = {};
        for (const key in captures) {
            if (Object.prototype.hasOwnProperty.call(captures, key)) {
                const { current, currentKeys } = captures[key];
                if (current !== undefined && currentKeys !== undefined) {
                    capturedPaths[key] = extractCapturedPath(value, currentKeys);
                }
            }
        }
        callback(capturedPaths);
    }
}
exports.matchPath = matchPath;
function extractCapturedPath(value, keys) {
    let capturedPath = value;
    for (const [i, key] of keys.entries()) {
        if (typeof key === 'string') {
            if (Array.isArray(capturedPath)) {
                throw new Error(`failed to get '${keys.join('.')}'; at '${keys
                    .slice(0, i + 1)
                    .join('.')}' expected a NodePath but got an array`);
            }
            capturedPath = capturedPath.get(key);
        }
        else if (typeof key === 'number') {
            if (!Array.isArray(capturedPath)) {
                throw new Error(`failed to get '${keys.join('.')}'; at '${keys
                    .slice(0, i + 1)
                    .join('.')}' expected an array but got a NodePath`);
            }
            capturedPath = capturedPath[key];
        }
        else {
            throw new Error(`failed to get '${keys.join('.')}'; key '${String(key)}' is neither a string nor a number, not ${typeof key}`);
        }
    }
    if (!Array.isArray(capturedPath) && typeof capturedPath.node !== 'object') {
        return capturedPath.node;
    }
    else {
        return capturedPath;
    }
}
//# sourceMappingURL=matchPath.js.map