{"name": "@codemod/matchers", "version": "1.7.1", "description": "Matchers for JavaScript & TypeScript codemods.", "repository": "https://github.com/codemod-js/codemod", "license": "Apache-2.0", "author": "<PERSON>", "main": "build/index.js", "types": "build/index.d.ts", "files": ["build"], "scripts": {"build": "tsc --build tsconfig.build.json", "clean": "rm -rf build tsconfig.build.tsbuildinfo", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "pnpm clean && pnpm build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "dependencies": {"@babel/types": "^7.20.7", "@codemod/utils": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/generator": "^7.20.14", "@babel/traverse": "^7.20.13", "@codemod/core": "^2.2.0", "@codemod/parser": "^1.4.0", "@types/babel__core": "^7.20.0", "@types/babel__generator": "^7.6.4", "@types/babel__template": "^7.4.1", "@types/babel__traverse": "^7.18.3", "@types/dedent": "^0.7.0", "@types/jest": "^25.1.0", "@types/node": "^18.14.0", "@types/prettier": "^2.0.0", "dedent": "^0.7.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "typescript": "^4.9.5"}, "publishConfig": {"access": "public"}, "gitHead": "21ef8147856dbd3525d1506eb94cd918662847a3"}