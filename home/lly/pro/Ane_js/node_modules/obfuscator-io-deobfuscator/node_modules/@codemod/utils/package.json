{"name": "@codemod/utils", "version": "1.1.0", "description": "codemod rewrites JavaScript and TypeScript", "repository": "https://github.com/codemod-js/codemod.git", "license": "Apache-2.0", "author": "<PERSON>", "main": "build/index.js", "types": "build/index.d.ts", "files": ["build"], "scripts": {"build": "tsc --build tsconfig.build.json", "clean": "rm -rf build tsconfig.build.tsbuildinfo", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "yarn clean && yarn build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "dependencies": {"@babel/core": "^7.20.12", "@babel/types": "^7.20.7", "@codemod/parser": "^1.4.0"}, "devDependencies": {"@types/babel__core": "^7.20.0", "@types/babel__generator": "^7.6.4", "@types/babel__traverse": "^7.18.3", "@types/jest": "^29.4.0", "@types/node": "^18.14.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1"}, "publishConfig": {"access": "public"}, "gitHead": "c7ba547a429235fb2682c6679877a38e53ebb832"}