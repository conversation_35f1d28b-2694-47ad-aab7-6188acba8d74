{"version": 3, "file": "matchPath.js", "sourceRoot": "", "sources": ["../../src/utils/matchPath.ts"], "names": [], "mappings": ";;;AAmDA,SAAgB,SAAS,CACvB,OAAsC,EACtC,QAA6B,EAC7B,KAA6C,EAC7C,QAA+C;IAE/C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAClC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QACtC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAA;IACd,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;QAC1B,MAAM,aAAa,GAAG,EAA0B,CAAA;QAEhD,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE;YAC1B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;gBACvD,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,QAAQ,CAAC,GAAc,CAAC,CAAA;gBACzD,IAAI,OAAO,KAAK,SAAS,IAAI,WAAW,KAAK,SAAS,EAAE;oBACtD,aAAa,CAAC,GAAc,CAAC,GAAG,mBAAmB,CACjD,KAAK,EACL,WAAW,CACZ,CAAA;iBACF;aACF;SACF;QAED,QAAQ,CAAC,aAAa,CAAC,CAAA;KACxB;AACH,CAAC;AA1BD,8BA0BC;AAED,SAAS,mBAAmB,CAC1B,KAAiD,EACjD,IAAgC;IAEhC,IAAI,YAAY,GAA+C,KAAK,CAAA;IAEpE,KAAK,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;QACrC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC/B,MAAM,IAAI,KAAK,CACb,kBAAkB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI;qBAC3C,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;qBACf,IAAI,CAAC,GAAG,CAAC,wCAAwC,CACrD,CAAA;aACF;YAED,YAAY,GAAG,YAAY,CAAC,GAAG,CAAC,GAAa,CAAC,CAAA;SAC/C;aAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAClC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAChC,MAAM,IAAI,KAAK,CACb,kBAAkB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI;qBAC3C,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;qBACf,IAAI,CAAC,GAAG,CAAC,wCAAwC,CACrD,CAAA;aACF;YAED,YAAY,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;SACjC;aAAM;YACL,MAAM,IAAI,KAAK,CACb,kBAAkB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,MAAM,CAC/C,GAAG,CACJ,2CAA2C,OAAO,GAAG,EAAE,CACzD,CAAA;SACF;KACF;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE;QACzE,OAAO,YAAY,CAAC,IAEN,CAAA;KACf;SAAM;QACL,OAAO,YAEO,CAAA;KACf;AACH,CAAC"}