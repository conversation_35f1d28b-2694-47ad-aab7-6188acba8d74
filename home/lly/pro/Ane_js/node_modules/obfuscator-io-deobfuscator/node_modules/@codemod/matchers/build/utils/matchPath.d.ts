import { NodePath } from '@babel/core';
import * as t from '@babel/types';
import * as m from '../matchers';
export type CapturedNodePaths<C> = {
    [K in keyof C]: C[K] extends t.Node ? NodePath<C[K]> : C[K];
};
export type CapturedMatchers<C> = {
    [K in keyof C]: m.CapturedMatcher<C[K]>;
};
/**
 * This helper makes it easier to use a matcher that captures `NodePath` values.
 * Here is an example codemod that removes a redundant `-1` argument on `slice`
 * calls:
 *
 * @example
 *
 * import * as m from '@codemod/matchers';
 * import { PluginObj } from '@babel/core';
 *
 * const negativeOneArgument = m.capture(m.numericLiteral(-1));
 * const sliceCallMatcher = m.callExpression(
 *   m.memberExpression(
 *     m.anyExpression(),
 *     m.identifier('slice'),
 *     false
 *   ),
 *   [m.anything(), negativeOneArgument]
 * );
 *
 * export default function(): PluginObj {
 *   return {
 *     CallExpression(path: NodePath<t.CallExpression>): void {
 *       m.matchPath(sliceCallMatcher, { negativeOneArgument }, path ({ negativeOneArgument }) => {
 *         negativeOneArgument.remove();
 *       });
 *     }
 *   };
 * }
 */
export declare function matchPath<Node extends t.Node, C extends m.CaptureBase>(matcher: m.Matcher<Node>, captures: CapturedMatchers<C>, value: NodePath<Node>, callback: (paths: CapturedNodePaths<C>) => void): void;
export declare function matchPath<Node extends t.Node, C extends m.CaptureBase>(matcher: m.Matcher<Array<Node>>, captures: CapturedMatchers<C>, value: Array<NodePath<Node>>, callback: (paths: CapturedNodePaths<C>) => void): void;
